# 📖 Roblox Lua Obfuscator Usage Guide

## 🎯 What Gets Obfuscated vs. What Gets Preserved

### ✅ **Preserved (Never Renamed)**
These elements remain unchanged to maintain Roblox functionality:

#### Core Roblox Globals
```lua
game, workspace, script, shared, _G
```

#### Roblox Services
```lua
Players, RunService, TweenService, UserInputService, ReplicatedStorage,
ServerStorage, StarterGui, StarterPack, StarterPlayer, Lighting,
SoundService, TextService, MarketplaceService, DataStoreService,
HttpService, TeleportService, MessagingService, MemoryStoreService,
PolicyService, LocalizationService, BadgeService, GroupService,
Chat, Teams, Debris, PathfindingService, CollectionService,
ContextActionService, GuiService, HapticService, VRService,
InsertService, ChangeHistoryService, Selection, CoreGui,
TestService, LogService, StatsService, NetworkClient, NetworkServer
```

#### Constructors & Data Types
```lua
Instance, Vector2, Vector3, CFrame, Color3, UDim, UDim2,
BrickColor, Ray, Region3, TweenInfo, NumberSequence, ColorSequence,
NumberRange, Rect, Axes, Faces, PhysicalProperties, Enum
```

#### Common Methods & Properties
```lua
:GetService(), :FindFirstChild(), :WaitForChild(), :Clone(), :Destroy(),
:Connect(), :Disconnect(), .LocalPlayer, .Character, .Humanoid,
.Position, .CFrame, .Size, .Color3, .Touched, .Changed, etc.
```

### 🔄 **Obfuscated (Gets Renamed)**
These elements are renamed to random identifiers:

#### User-Defined Variables
```lua
-- Original
local myVariable = "Hello"
local playerSpeed = 16

-- Obfuscated
local kXmNpQwErTy = "Hello"
local aSdFgHjKlZx = 16
```

#### User-Defined Functions
```lua
-- Original
function teleportPlayer(position)
    -- code here
end

-- Obfuscated
function hGfDsAqWeR(aSdFgHjKl)
    -- code here
end
```

#### Function Parameters
```lua
-- Original
function myFunction(player, position, speed)
    -- code here
end

-- Obfuscated
function qWeRtYuIoP(zXcVbN, mQwErT, yUiOpA)
    -- code here
end
```

## 🔐 Obfuscation Layers

### Layer 1: String Encoding
All string literals are Base64 encoded with noise injection:

```lua
-- Original
local message = "Welcome to my game!"

-- Obfuscated
local stringArray = {"V2VsY29tZSB0byBteSBnYW1lIQ=="}
local message = decode(stringArray[1])
```

### Layer 2: Variable Renaming
User variables get random 8-13 character names:

```lua
-- Original
local playerData = {
    name = "Player1",
    level = 5,
    coins = 100
}

-- Obfuscated
local kXmNpQwErTyU = {
    name = decode(stringArray[1]),
    level = 5,
    coins = 100
}
```

### Layer 3: Function Dispatching
Function calls are routed through a dispatcher:

```lua
-- Original
print("Hello World")

-- Obfuscated
dispatcher(1, decode(stringArray[2]))
```

## 🎮 Roblox-Specific Examples

### Example 1: Player Management
```lua
-- Original Script
local Players = game:GetService("Players")
local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()

local walkSpeed = 20
local jumpPower = 60

function setPlayerStats(speed, jump)
    local humanoid = character:WaitForChild("Humanoid")
    humanoid.WalkSpeed = speed
    humanoid.JumpPower = jump
    print("Player stats updated!")
end

setPlayerStats(walkSpeed, jumpPower)
```

```lua
-- Obfuscated Output
local stringArray={"UGxheWVyIHN0YXRzIHVwZGF0ZWQh"};
local function decode(s)return base64decode(removeNoise(s))end;
local Players=game:GetService("Players")
local kXmNpQwErTy=Players.LocalPlayer
local aSdFgHjKlZx=kXmNpQwErTy.Character or kXmNpQwErTy.CharacterAdded:Wait()
local zXcVbNmQwE=20
local rTyUiOpAsDf=60
function qWeRtYuIoP(mNbVcXz,lKjHgFdS)
    local aQwErTyUi=aSdFgHjKlZx:WaitForChild("Humanoid")
    aQwErTyUi.WalkSpeed=mNbVcXz
    aQwErTyUi.JumpPower=lKjHgFdS
    print(decode(stringArray[1]))
end
qWeRtYuIoP(zXcVbNmQwE,rTyUiOpAsDf)
```

### Example 2: GUI Creation
```lua
-- Original Script
local Players = game:GetService("Players")
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local screenGui = Instance.new("ScreenGui")
screenGui.Name = "MyGui"
screenGui.Parent = playerGui

local frame = Instance.new("Frame")
frame.Size = UDim2.new(0.3, 0, 0.2, 0)
frame.Position = UDim2.new(0.35, 0, 0.4, 0)
frame.BackgroundColor3 = Color3.new(0, 0, 1)
frame.Parent = screenGui

local textLabel = Instance.new("TextLabel")
textLabel.Text = "Welcome to the game!"
textLabel.Size = UDim2.new(1, 0, 1, 0)
textLabel.BackgroundTransparency = 1
textLabel.TextColor3 = Color3.new(1, 1, 1)
textLabel.Parent = frame
```

The obfuscated version will preserve all Roblox API calls while renaming your custom variables.

## ⚡ Best Practices

### 1. Test Before Publishing
Always test your obfuscated scripts in Roblox Studio:
```lua
-- Test in Studio first
-- Then publish to your game
```

### 2. Keep Backups
```bash
# Always keep your original source code
original_script.lua
obfuscated_script.lua
```

### 3. Use Meaningful Comments (They'll Be Removed)
```lua
-- This comment will be removed during obfuscation
-- Use comments in your original code for documentation
local myVariable = "value" -- This helps during development
```

### 4. Avoid Global Variables When Possible
```lua
-- Prefer local variables (they get better obfuscation)
local myVar = "value"

-- Instead of global variables
myGlobalVar = "value" -- Less secure
```

## 🚫 Limitations

### What Cannot Be Obfuscated
1. **Roblox API Names**: Service names, method names, property names
2. **Lua Keywords**: `local`, `function`, `if`, `then`, `end`, etc.
3. **Built-in Functions**: `print`, `pairs`, `ipairs`, `type`, etc.
4. **String Content**: The actual content of strings (only the variable names change)

### Performance Considerations
- **Minimal Impact**: Most scripts see negligible performance impact
- **Complex Scripts**: Very large scripts may have slight overhead from decoding
- **Memory Usage**: Encoded strings may use slightly more memory

## 🔍 Debugging Obfuscated Code

If you need to debug obfuscated code:
1. Use the original source code for debugging
2. Add print statements before obfuscation
3. Use Roblox Studio's output window
4. Test incrementally with smaller code sections

---

**Happy Obfuscating! 🎮🔐**
