<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roblox Lua Script Obfuscator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔒 Roblox Lua Script Obfuscator</h1>
            <p>Transform your Roblox Lua scripts into heavily obfuscated code optimized for Roblox Studio</p>
        </header>

        <main>
            <div class="editor-section">
                <div class="input-panel">
                    <div class="panel-header">
                        <h3>📝 Original Roblox Lua Code</h3>
                        <div class="panel-actions">
                            <button id="clearInput" class="btn btn-secondary">Clear</button>
                            <button id="loadExample" class="btn btn-secondary">Load Example</button>
                        </div>
                    </div>
                    <textarea 
                        id="inputCode" 
                        placeholder="-- Enter your Roblox Lua code here
local Players = game:GetService('Players')
local player = Players.LocalPlayer

print('Hello, ' .. player.Name .. '!')
wait(1)
print('Welcome to Roblox!')"
                        spellcheck="false"
                    ></textarea>
                    <div class="input-info">
                        <span id="inputStats">Lines: 0 | Characters: 0</span>
                    </div>
                </div>

                <div class="controls">
                    <button id="obfuscateBtn" class="btn btn-primary">
                        <span class="btn-text">🔐 Obfuscate Code</span>
                        <span class="btn-loading" style="display: none;">⏳ Processing...</span>
                    </button>
                </div>

                <div class="output-panel">
                    <div class="panel-header">
                        <h3>🛡️ Obfuscated Output</h3>
                        <div class="panel-actions">
                            <button id="copyOutput" class="btn btn-success" disabled>📋 Copy</button>
                            <button id="downloadOutput" class="btn btn-secondary" disabled>💾 Download</button>
                        </div>
                    </div>
                    <textarea 
                        id="outputCode" 
                        readonly 
                        placeholder="Obfuscated code will appear here..."
                    ></textarea>
                    <div class="output-info">
                        <span id="outputStats">Ready to obfuscate</span>
                        <span id="compressionRatio"></span>
                    </div>
                </div>
            </div>

            <div class="features-section">
                <h3>🔧 Obfuscation Features</h3>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎮</div>
                        <h4>Roblox Optimized</h4>
                        <p>Preserves Roblox services, APIs, and game objects</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔐</div>
                        <h4>Multi-Layer Encryption</h4>
                        <p>Base64 encoding + XOR encryption with random keys</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🌀</div>
                        <h4>Smart Obfuscation</h4>
                        <p>Renames variables while preserving Roblox functionality</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h4>Studio Compatible</h4>
                        <p>Works seamlessly in Roblox Studio and live games</p>
                    </div>
                </div>
            </div>

            <div class="warning-section">
                <div class="warning-box">
                    <h4>⚠️ Important Notes</h4>
                    <ul>
                        <li>Always test your obfuscated code in Roblox Studio before publishing</li>
                        <li>Keep a backup of your original source code</li>
                        <li>Obfuscation may slightly impact performance in complex scripts</li>
                        <li>This tool preserves Roblox API functionality and game compatibility</li>
                    </ul>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2024 Roblox Lua Obfuscator | Built for Roblox developers and secure code protection</p>
        </footer>
    </div>

    <div id="notification" class="notification"></div>

    <script src="script.js"></script>
</body>
</html>
