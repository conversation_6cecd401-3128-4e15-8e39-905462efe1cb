{"name": "roblox-lua-obfuscator", "version": "1.0.0", "description": "Advanced Roblox Lua script obfuscator with multi-layer encryption, Base64 encoding, and Roblox API preservation", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["roblo<PERSON>", "lua", "obfuscation", "script", "protection", "encryption", "minification"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "express": "^5.1.0", "luaparse": "^0.3.1", "node-fetch": "^2.7.0"}}