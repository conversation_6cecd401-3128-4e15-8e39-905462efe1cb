class LuaObfuscatorUI {
    constructor() {
        this.initializeElements();
        this.attachEventListeners();
        this.updateInputStats();
    }

    initializeElements() {
        this.inputCode = document.getElementById('inputCode');
        this.outputCode = document.getElementById('outputCode');
        this.obfuscateBtn = document.getElementById('obfuscateBtn');
        this.copyOutput = document.getElementById('copyOutput');
        this.downloadOutput = document.getElementById('downloadOutput');
        this.clearInput = document.getElementById('clearInput');
        this.loadExample = document.getElementById('loadExample');
        this.inputStats = document.getElementById('inputStats');
        this.outputStats = document.getElementById('outputStats');
        this.compressionRatio = document.getElementById('compressionRatio');
        this.notification = document.getElementById('notification');
    }

    attachEventListeners() {
        this.obfuscateBtn.addEventListener('click', () => this.obfuscateCode());
        this.copyOutput.addEventListener('click', () => this.copyToClipboard());
        this.downloadOutput.addEventListener('click', () => this.downloadCode());
        this.clearInput.addEventListener('click', () => this.clearInput());
        this.loadExample.addEventListener('click', () => this.loadExampleCode());
        this.inputCode.addEventListener('input', () => this.updateInputStats());
        this.inputCode.addEventListener('keydown', (e) => this.handleKeyDown(e));
    }

    updateInputStats() {
        const code = this.inputCode.value;
        const lines = code.split('\n').length;
        const chars = code.length;
        this.inputStats.textContent = `Lines: ${lines} | Characters: ${chars}`;
    }

    handleKeyDown(e) {
        // Tab key support
        if (e.key === 'Tab') {
            e.preventDefault();
            const start = this.inputCode.selectionStart;
            const end = this.inputCode.selectionEnd;
            const value = this.inputCode.value;
            
            this.inputCode.value = value.substring(0, start) + '    ' + value.substring(end);
            this.inputCode.selectionStart = this.inputCode.selectionEnd = start + 4;
        }
    }

    async obfuscateCode() {
        const code = this.inputCode.value.trim();
        
        if (!code) {
            this.showNotification('Please enter some Roblox Lua code to obfuscate', 'error');
            return;
        }

        this.setLoading(true);

        try {
            const response = await fetch('/api/obfuscate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ code })
            });

            const result = await response.json();

            if (result.success) {
                this.outputCode.value = result.obfuscated;
                this.updateOutputStats(result);
                this.copyOutput.disabled = false;
                this.downloadOutput.disabled = false;
                this.showNotification('Code obfuscated successfully!', 'success');
            } else {
                this.showNotification(`Obfuscation failed: ${result.error}`, 'error');
                this.outputCode.value = '';
                this.outputStats.textContent = 'Obfuscation failed';
                this.copyOutput.disabled = true;
                this.downloadOutput.disabled = true;
            }
        } catch (error) {
            this.showNotification(`Network error: ${error.message}`, 'error');
            this.outputCode.value = '';
            this.outputStats.textContent = 'Network error';
            this.copyOutput.disabled = true;
            this.downloadOutput.disabled = true;
        } finally {
            this.setLoading(false);
        }
    }

    updateOutputStats(result) {
        const reduction = ((result.originalSize - result.obfuscatedSize) / result.originalSize * 100).toFixed(1);
        const ratio = (result.obfuscatedSize / result.originalSize).toFixed(2);

        let statsText = `Characters: ${result.obfuscatedSize}`;

        // Add VM statistics if available
        if (result.vmInstructions !== undefined && result.vmConstants !== undefined) {
            statsText += ` | VM Instructions: ${result.vmInstructions} | VM Constants: ${result.vmConstants}`;
        }

        this.outputStats.textContent = statsText;

        if (result.obfuscatedSize < result.originalSize) {
            this.compressionRatio.textContent = `${reduction}% smaller`;
            this.compressionRatio.style.color = '#28a745';
        } else {
            const increase = ((result.obfuscatedSize - result.originalSize) / result.originalSize * 100).toFixed(1);
            this.compressionRatio.textContent = `${increase}% larger`;
            this.compressionRatio.style.color = '#dc3545';
        }
    }

    async copyToClipboard() {
        try {
            await navigator.clipboard.writeText(this.outputCode.value);
            this.showNotification('Obfuscated code copied to clipboard!', 'success');
        } catch (error) {
            // Fallback for older browsers
            this.outputCode.select();
            document.execCommand('copy');
            this.showNotification('Obfuscated code copied to clipboard!', 'success');
        }
    }

    downloadCode() {
        const code = this.outputCode.value;
        const blob = new Blob([code], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'obfuscated_script.lua';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('Obfuscated code downloaded!', 'success');
    }

    clearInput() {
        this.inputCode.value = '';
        this.outputCode.value = '';
        this.updateInputStats();
        this.outputStats.textContent = 'Ready to obfuscate';
        this.compressionRatio.textContent = '';
        this.copyOutput.disabled = true;
        this.downloadOutput.disabled = true;
        this.inputCode.focus();
    }

    loadExampleCode() {
        const exampleCode = `-- Example Roblox Lua Script
print("Welcome to Roblox Lua Obfuscator!")

-- Get Roblox services
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local Workspace = game:GetService("Workspace")

-- Get player and character
local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()
local humanoid = character:WaitForChild("Humanoid")

-- Variables and basic operations
local playerName = player.Name
local walkSpeed = 16
local jumpPower = 50

-- Function to create a part
function createPart(name, size, position)
    local part = Instance.new("Part")
    part.Name = name
    part.Size = size
    part.Position = position
    part.Anchored = true
    part.BrickColor = BrickColor.new("Bright blue")
    part.Parent = Workspace
    return part
end

-- Create some parts
local parts = {}
for i = 1, 5 do
    local part = createPart("Part" .. i, Vector3.new(4, 1, 4), Vector3.new(i * 6, 5, 0))
    table.insert(parts, part)
end

-- Tween example
local tweenInfo = TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
for _, part in ipairs(parts) do
    local tween = TweenService:Create(part, tweenInfo, {Position = part.Position + Vector3.new(0, 10, 0)})
    tween:Play()
end

-- Player interaction
print("Player: " .. playerName .. " has joined!")
print("Character loaded successfully!")

-- Set player properties
humanoid.WalkSpeed = walkSpeed
humanoid.JumpPower = jumpPower

-- Conditional logic
if userData.active then
    print("User is currently active")
else
    print("User is inactive")
end`;

        this.inputCode.value = exampleCode;
        this.updateInputStats();
        this.showNotification('Example code loaded!', 'info');
    }

    setLoading(loading) {
        const btnText = this.obfuscateBtn.querySelector('.btn-text');
        const btnLoading = this.obfuscateBtn.querySelector('.btn-loading');
        
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline';
            this.obfuscateBtn.disabled = true;
        } else {
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            this.obfuscateBtn.disabled = false;
        }
    }

    showNotification(message, type = 'info') {
        this.notification.textContent = message;
        this.notification.className = `notification ${type}`;
        this.notification.classList.add('show');
        
        setTimeout(() => {
            this.notification.classList.remove('show');
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LuaObfuscatorUI();
});
