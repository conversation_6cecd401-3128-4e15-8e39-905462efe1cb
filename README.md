# 🎮 Roblox Lua Script Obfuscator

A powerful, web-based Lua script obfuscator specifically designed for Roblox developers. This tool provides advanced multi-layer obfuscation while preserving Roblox API functionality and game compatibility.

## ✨ Features

### 🎯 **Roblox-Optimized**
- **Smart API Preservation**: Automatically preserves all Roblox services, globals, and methods
- **Game Compatibility**: Ensures obfuscated scripts work seamlessly in Roblox Studio and live games
- **Service Recognition**: Recognizes and preserves `game:GetService()` calls and common Roblox objects

### 🔐 **Advanced Obfuscation**
- **Multi-Layer Encryption**: Base64 encoding + XOR encryption with random keys
- **String Encoding**: All string literals are encoded and stored in encrypted arrays
- **Variable Renaming**: User-defined variables and functions get randomized names (8-13 characters)
- **Function Dispatching**: Adds an extra layer of obfuscation through function call routing
- **Noise Injection**: Random characters added to encoded strings for extra security

### 🛡️ **Preserved Roblox Elements**
- **Core Globals**: `game`, `workspace`, `script`, `shared`, `_G`
- **Services**: `Players`, `RunService`, `TweenService`, `UserInputService`, etc.
- **Constructors**: `Instance.new`, `Vector3.new`, `CFrame.new`, `Color3.new`, etc.
- **Methods**: `:GetService()`, `:FindFirstChild()`, `:WaitForChild()`, `:Connect()`, etc.
- **Properties**: `.LocalPlayer`, `.Character`, `.Humanoid`, `.Position`, etc.

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd roblox-lua-obfuscator

# Install dependencies
npm install

# Start the server
npm start
```

### Usage
1. Open your browser and navigate to `http://localhost:3000`
2. Paste your Roblox Lua script in the input area
3. Click "🔐 Obfuscate Code"
4. Copy the obfuscated output and use it in your Roblox game

## 📝 Example

### Original Roblox Script:
```lua
local Players = game:GetService("Players")
local player = Players.LocalPlayer
local character = player.Character

-- Custom variables
local mySpeed = 50
local myMessage = "Hello World!"

-- Custom function
function teleportPlayer(position)
    if character and character:FindFirstChild("HumanoidRootPart") then
        character.HumanoidRootPart.CFrame = CFrame.new(position)
        print(myMessage)
    end
end

-- Usage
teleportPlayer(Vector3.new(0, 10, 0))
```

### Obfuscated Output:
```lua
local kXmNpQwErTy={"SGVsbG8gV29ybGQh","dGVsZXBvcnRQbGF5ZXI="};
local function rTyUiOpAsDf(s)return game:GetService("HttpService"):JSONDecode(s)end;
local function qWeRtYuIoP(s)return string.char(unpack(s))end;
local Players=game:GetService("Players")
local aSdFgHjKlZx=Players.LocalPlayer
local zXcVbNmQwE=aSdFgHjKlZx.Character
local rTyUiOpLkJ=50
local qWeRtYuMnB=rTyUiOpAsDf(kXmNpQwErTy[1])
function hGfDsAqWeR(aSdFgHjKl)
    if zXcVbNmQwE and zXcVbNmQwE:FindFirstChild("HumanoidRootPart")then
        zXcVbNmQwE.HumanoidRootPart.CFrame=CFrame.new(aSdFgHjKl)
        print(qWeRtYuMnB)
    end
end
hGfDsAqWeR(Vector3.new(0,10,0))
```

## 🔧 Technical Details

### Obfuscation Techniques
1. **Identifier Randomization**: All user-defined variables and functions are renamed to random 8-13 character strings
2. **String Encryption**: String literals are Base64 encoded with noise injection
3. **Function Dispatching**: Function calls are routed through a dispatcher system
4. **Code Minification**: Removes comments, unnecessary whitespace, and optimizes structure

### Roblox API Preservation
The obfuscator maintains a comprehensive list of Roblox-specific elements that should never be renamed:
- 40+ Roblox services
- 20+ constructor functions
- 30+ common methods and properties
- All Lua standard library functions

## ⚠️ Important Notes

- **Always test** your obfuscated scripts in Roblox Studio before publishing
- **Keep backups** of your original source code
- **Performance impact** may occur with very complex scripts
- **Roblox compatibility** is maintained for all standard Roblox APIs

## 🛠️ Development

### Project Structure
```
roblox-lua-obfuscator/
├── luamin.js          # Core obfuscation engine
├── server.js          # Express server
├── public/            # Web interface
│   ├── index.html     # Main UI
│   ├── script.js      # Frontend logic
│   └── styles.css     # Styling
└── package.json       # Dependencies
```

### Key Files
- **`luamin.js`**: Modified luamin library with Roblox-specific optimizations
- **`server.js`**: Express server handling obfuscation requests
- **`public/`**: Web-based user interface

## 📄 License

This project is licensed under the ISC License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## 🎯 Use Cases

- **Script Protection**: Protect your valuable Roblox scripts from theft
- **Anti-Reverse Engineering**: Make it difficult for others to understand your code
- **Competitive Advantage**: Keep your game mechanics and algorithms secret
- **Educational**: Learn about code obfuscation techniques

---

**Built for Roblox developers by Roblox developers** 🎮
